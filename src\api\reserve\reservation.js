import request from '@/utils/request'

// 查询会议室预约申请列表
export function listReservation(query) {
  return request({
    url: '/reserve/reservation/list',
    method: 'get',
    params: query
  })
}

// 查询会议室预约申请详细
export function getReservation(reservationId) {
  return request({
    url: '/reserve/reservation/' + reservationId,
    method: 'get'
  })
}

// 新增会议室预约申请
export function addReservation(data) {
  return request({
    url: '/reserve/reservation',
    method: 'post',
    data: data
  })
}

// 修改会议室预约申请
export function updateReservation(data) {
  return request({
    url: '/reserve/reservation',
    method: 'put',
    data: data
  })
}

// 删除会议室预约申请
export function delReservation(reservationId) {
  return request({
    url: '/reserve/reservation/' + reservationId,
    method: 'delete'
  })
}

// 审批预约
export function approveReservation(data) {
  return request({
    url: '/reserve/reservation/approve',
    method: 'put',
    data: data
  })
}

// 批量审批
export function batchApprove(data) {
  return request({
    url: '/reserve/reservation/batchApprove',
    method: 'put',
    data: data
  })
}

// 强制取消
export function forceCancel(reservationIds) {
  return request({
    url: '/reserve/reservation/forceCancel/' + reservationIds,
    method: 'put'
  })
}

// 查询我的预约列表
export function myReservationList(query) {
  return request({
    url: '/reserve/reservation/myList',
    method: 'get',
    params: query
  })
}

// 取消预约
export function cancelReservation(reservationId) {
  return request({
    url: '/reserve/reservation/cancel/' + reservationId,
    method: 'put'
  })
}
