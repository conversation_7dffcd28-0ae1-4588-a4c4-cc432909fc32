<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="全部预约" name="all">
        <!-- 现有的预约列表 -->
      </el-tab-pane>
      <el-tab-pane label="我的预约" name="my">
        <el-table :data="myReservationListData">
          <!-- 表格列定义 -->
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getMyList"
        />
      </el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议室ID" prop="roomId">
        <el-input
          v-model="queryParams.roomId"
          placeholder="请输入会议室ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
       <el-form-item label="申请人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reserve:reservation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reserve:reservation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchApprove"
          v-hasPermi="['reserve:reservation:approve']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchReject"
          v-hasPermi="['reserve:reservation:approve']"
        >批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleForceCancel"
          v-hasPermi="['reserve:reservation:cancel']"
        >强制取消</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['reserve:reservation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reservationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预约ID" align="center" prop="reservationId" />
      <el-table-column label="会议室ID" align="center" prop="roomId" />
      <el-table-column label="申请人姓名" align="center" prop="userName" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="会议主题" align="center" prop="meetingTitle" />
      <el-table-column label="会议开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态：0-取消，1-待审核，2-已通过，3-已拒绝，4-已完成" align="center" prop="status" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['reserve:reservation:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['reserve:reservation:remove']">删除</el-button>
          <el-button
             v-if="scope.row.status == 1"
             link
             type="success"
             icon="Check"
             @click="handleApprove(scope.row, 2)"
             v-hasPermi="['reserve:reservation:approve']"
            >通过</el-button>
          <el-button
            link
            type="warning"
            icon="Close"
            @click="handleApprove(scope.row, 3)"
            v-hasPermi="['reserve:reservation:approve']"
            v-if="scope.row.status == 1"
          >拒绝</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleForceCancel(scope.row)"
            v-hasPermi="['reserve:reservation:cancel']"
            v-if="scope.row.status != 0"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="activeTab === 'all' ? getList : getMyList"
    />

    <!-- 添加或修改会议室预约申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会议室ID" prop="roomId">
          <el-input v-model="form.roomId" placeholder="请输入会议室ID" />
        </el-form-item>
        <el-form-item label="申请人ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人ID" />
        </el-form-item>
        <el-form-item label="申请人姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入申请人姓名" />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTitle">
          <el-input v-model="form.meetingTitle" placeholder="请输入会议主题" />
        </el-form-item>
        <el-form-item label="会议开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择会议开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会议结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择会议结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批意见" prop="approveComment">
          <el-input v-model="form.approveComment" type="textarea" placeholder="请输入审批意见" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Reservation">
import { ref, reactive, toRefs, getCurrentInstance, watch } from "vue";
import { 
  listReservation, getReservation, delReservation, 
  addReservation, updateReservation, approveReservation, 
  myReservationList 
} from "@/api/reserve/reservation";

// Vue实例代理
const { proxy } = getCurrentInstance();

// 状态选项
const statusOptions = [
  { dictLabel: '取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// UI相关
const reservationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const activeTab = ref("all")
const myReservationListData = ref([])

// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomId: null,
    status: null,
  },
  rules: {
    roomId: [{ required: true, message: "会议室ID不能为空", trigger: "blur" }],
    userId: [{ required: true, message: "申请人ID不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "申请人姓名不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "部门ID不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "会议开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "会议结束时间不能为空", trigger: "blur" }],
  }
})

const { queryParams, form, rules } = toRefs(data)

// ========== 核心方法 ==========

function getList() {
  loading.value = true
  listReservation(queryParams.value).then(response => {
    reservationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

function getMyList() {
  (queryParams.value).then(response => {
    myReservationListData.value = response.rows
    total.value = response.total
  })
}

function handleApprove(row, status) {
  proxy.$confirm(`确认要${status === 2 ? '通过' : '拒绝'}该预约吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const data = {
      reservationId: row.reservationId,
      status: status,
      approveComment: `${status === 2 ? '通过' : '拒绝'}审批`
    }
    return approveReservation(data)
  }).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleBatchApprove() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要批量通过的预约")
    return
  }
  proxy.$confirm("确认要批量通过选中的预约吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const promises = ids.value.map(id => {
      return approveReservation({
        reservationId: id,
        status: 2,
        approveComment: "批量通过审批"
      })
    })
    return Promise.all(promises)
  }).then(() => {
    proxy.$modal.msgSuccess("批量通过操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleBatchReject() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请选择要批量拒绝的预约")
    return
  }
  proxy.$confirm("确认要批量拒绝选中的预约吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const promises = ids.value.map(id => {
      return approveReservation({
        reservationId: id,
        status: 3,
        approveComment: "批量拒绝审批"
      })
    })
    return Promise.all(promises)
  }).then(() => {
    proxy.$modal.msgSuccess("批量拒绝操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function handleForceCancel(row) {
  const _reservationIds = row ? row.reservationId : ids.value
  proxy.$confirm(`确认要${row ? '取消' : '批量取消'}选中的预约吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return approveReservation({
      reservationId: _reservationIds,
      status: 0,
      approveComment: "强制取消预约"
    })
  }).then(() => {
    proxy.$modal.msgSuccess("取消操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

function cancel() {
  open.value = false
  reset()
}

function reset() {
  form.value = {
    reservationId: null,
    roomId: null,
    userId: null,
    userName: null,
    deptId: null,
    deptName: null,
    meetingTitle: null,
    startTime: null,
    endTime: null,
    attendees: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("reservationRef")
}

function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.reservationId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会议室预约申请"
}

function handleUpdate(row) {
  reset()
  const _reservationId = row.reservationId || ids.value
  getReservation(_reservationId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会议室预约申请"
  })
}

function submitForm() {
  proxy.$refs["reservationRef"].validate(valid => {
    if (valid) {
      if (form.value.reservationId != null) {
        updateReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

function handleDelete(row) {
  const _reservationIds = row.reservationId || ids.value
  proxy.$modal.confirm(`是否确认删除会议室预约申请编号为 "${_reservationIds}" 的数据项？`)
    .then(() => delReservation(_reservationIds))
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    }).catch(() => {})
}

function handleExport() {
  proxy.download('reserve/reservation/export', {
    ...queryParams.value
  }, `reservation_${new Date().getTime()}.xlsx`)
}

// 监听 tab 切换
watch(activeTab, (val) => {
  if (val === 'my') {
    getMyList()
  }
})

// 初始化数据
getList()
</script>
