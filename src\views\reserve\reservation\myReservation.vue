<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议室" prop="roomId">
        <el-input
          v-model="queryParams.roomId"
          placeholder="请输入会议室ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['reserve:reservation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleSearch">
          {{ showSearch ? '隐藏搜索' : '显示搜索' }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reservationList">
      <el-table-column label="会议室" align="center" prop="roomName" />
      <el-table-column label="会议主题" align="center" prop="meetingTitle" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="statusTagType(scope.row.status)">
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['reserve:reservation:edit']"
            v-if="scope.row.status == 1"
          >修改</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleCancel(scope.row)"
            v-if="scope.row.status != 0"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会议室预约申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会议室ID" prop="roomId">
          <el-input v-model="form.roomId" placeholder="请输入会议室ID" />
        </el-form-item>
        <el-form-item label="申请人ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人ID" disabled />
        </el-form-item>
        <el-form-item label="申请人姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入申请人姓名" disabled />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" disabled />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" disabled />
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTitle">
          <el-input v-model="form.meetingTitle" placeholder="请输入会议主题" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择会议开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择会议结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参会人员" prop="attendees">
          <el-input v-model="form.attendees" type="textarea" placeholder="请输入参会人员" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MyReservation">
import { ref, reactive, toRefs } from 'vue'
import { myReservationList, cancelReservation, addReservation, updateReservation, getReservation } from "@/api/reserve/reservation"
import { getCurrentInstance } from 'vue'
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 状态选项
const statusOptions = [
  { dictLabel: '取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// 数据定义
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const dateRange = ref([])
const reservationList = ref([])
const open = ref(false)
const title = ref("")

// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomId: null,
    status: null,
    // 会自动添加当前用户ID
  },
  rules: {
    roomId: [{ required: true, message: "会议室ID不能为空", trigger: "blur" }],
    userId: [{ required: true, message: "申请人ID不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "申请人姓名不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "部门ID不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "会议开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "会议结束时间不能为空", trigger: "blur" }],
  }
})

const { form, queryParams, rules } = toRefs(data)

/** 查询我的预约列表 */
function getList() {
  loading.value = true
  // 自动添加当前用户ID
  queryParams.value.userId = userStore.id
  myReservationList(queryParams.value).then(response => {
    reservationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '已取消', 1: '待审核', 2: '已通过', 3: '已拒绝', 4: '已完成' }
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: '' }
  return map[status]
}

/** 表单重置 */
function reset() {
  form.value = {
    reservationId: null,
    roomId: null,
    userId: null,
    userName: null,
    deptId: null,
    deptName: null,
    meetingTitle: null,
    startTime: null,
    endTime: null,
    attendees: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("reservationRef")
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  // 自动填充当前用户信息
  form.value.userId = userStore.id
  form.value.userName = userStore.name
  form.value.deptId = userStore.deptId
  form.value.deptName = userStore.deptName
  open.value = true
  title.value = "添加会议室预约申请"
}

/** 修改预约 */
function handleUpdate(row) {
  reset()
  const _reservationId = row.reservationId
  getReservation(_reservationId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会议室预约申请"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reservationRef"].validate(valid => {
    if (valid) {
      if (form.value.reservationId != null) {
        updateReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 切换搜索显示 */
function toggleSearch() {
  showSearch.value = !showSearch.value
}

/** 取消预约 */
function handleCancel(row) {
  proxy.$modal.confirm('确认要取消该预约吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return cancelReservation(row.reservationId)
  }).then(() => {
    proxy.$modal.msgSuccess("取消成功")
    getList()
  }).catch(() => {})
}

// 初始化加载
getList()
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>