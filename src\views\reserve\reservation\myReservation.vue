<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议室" prop="roomId">
        <el-input
          v-model="queryParams.roomId"
          placeholder="请输入会议室ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="reservationList">
      <el-table-column label="会议室" align="center" prop="roomName" />
      <el-table-column label="会议主题" align="center" prop="meetingTitle" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="statusTagType(scope.row.status)">
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['reserve:reservation:edit']"
            v-if="scope.row.status == 1"
          >修改</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleCancel(scope.row)"
            v-if="scope.row.status != 0"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="MyReservation">
import { ref, reactive, toRefs } from 'vue'
import { myReservationList, cancelReservation } from "@/api/reserve/reservation"
import { getCurrentInstance } from 'vue'
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 状态选项
const statusOptions = [
  { dictLabel: '取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// 数据定义
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const dateRange = ref([])
const reservationList = ref([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomId: null,
    status: null,
    // 会自动添加当前用户ID
  }
})

const { queryParams } = toRefs(data)

/** 查询我的预约列表 */
function getList() {
  loading.value = true
  // 自动添加当前用户ID
  queryParams.value.userId = userStore.id
  myReservationList(queryParams.value).then(response => {
    reservationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '已取消', 1: '待审核', 2: '已通过', 3: '已拒绝', 4: '已完成' }
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: '' }
  return map[status]
}

/** 修改预约 */
function handleUpdate(row) {
  // 这里可以跳转到编辑页面或打开编辑对话框
  // 暂时使用简单的提示，具体实现需要根据业务需求
  proxy.$modal.msgInfo("编辑功能待实现")
}

/** 取消预约 */
function handleCancel(row) {
  proxy.$modal.confirm('确认要取消该预约吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return cancelReservation(row.reservationId)
  }).then(() => {
    proxy.$modal.msgSuccess("取消成功")
    getList()
  }).catch(() => {})
}

// 初始化加载
getList()
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>